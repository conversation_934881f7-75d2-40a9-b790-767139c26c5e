<?php

/**
 * Plugin Name: Simple Content Paywall
 * Description: A simple paywall system that shows first 2 paragraphs then paywall
 * Version: 1.0.0
 * Author: Alfons Digital
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class AlfonsPaywall
{

    public function __construct()
    {
        add_action('plugins_loaded', array($this, 'init'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_filter('the_content', array($this, 'filter_content'));
        add_action('add_meta_boxes', array($this, 'add_meta_boxes'));
        add_action('save_post', array($this, 'save_post_meta'));

        // Try multiple hooks to ensure meta box is added
        add_action('admin_menu', array($this, 'add_meta_boxes'));
        add_action('add_meta_boxes_post', array($this, 'add_meta_boxes'));
        add_action('add_meta_boxes_page', array($this, 'add_meta_boxes'));

        // ACF Integration
        add_action('acf/init', array($this, 'setup_acf_options'));

        // Admin post list integration
        add_filter('the_title', array($this, 'add_lock_to_admin_title'), 10, 2);

        // Quick Edit integration
        add_action('quick_edit_custom_box', array($this, 'add_quick_edit_field'), 10, 2);
        add_action('wp_ajax_save_paywall_quick_edit', array($this, 'save_quick_edit'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));

        // Add a paywall column for the `posts` post type    
        add_filter('manage_posts_columns', 'add_paywall_column', 10, 2);
        function add_paywall_column($posts_columns, $post_type)
        {
            $posts_columns['paywall'] = '';
            return $posts_columns;
        }
        // But remove it again on the edit screen (other screens to?)
        add_filter('manage_edit-post_columns', 'remove_paywall_column');
        function remove_paywall_column($posts_columns)
        {
            unset($posts_columns['paywall']);
            return $posts_columns;
        }

        // Add our text to the quick edit box
        // add_action('quick_edit_custom_box', array($this, 'add_quick_edit_field'), 10, 2);
    }

    public function init()
    {
        // Plugin initialization
    }

    public function add_lock_to_admin_title($title, $post_id = null)
    {
        // Only modify titles in admin area
        if (!is_admin()) {
            return $title;
        }

        // Only on post list screens
        global $pagenow;
        if ($pagenow !== 'edit.php' && $pagenow !== 'edit-tags.php') {
            return $title;
        }

        // Get current screen to make sure we're on post list
        $screen = get_current_screen();
        if (!$screen || !in_array($screen->base, array('edit', 'edit-tags'))) {
            return $title;
        }

        // Skip if no post ID
        if (!$post_id) {
            return $title;
        }

        // Check if this post has paywall enabled
        $is_paywalled = get_post_meta($post_id, '_paywall_enabled', true);

        if ($is_paywalled) {
            $title = '🔒 ' . $title;
        }

        return $title;
    }

    public function enqueue_scripts()
    {
        wp_enqueue_style('paywall-style', $this->get_plugin_url() . 'style.css', array(), '1.0.0');
    }

    private function get_plugin_url()
    {
        return plugin_dir_url(__FILE__);
    }

    public function setup_acf_options()
    {
        if (!function_exists('acf_add_options_page')) {
            return;
        }

        // Add options page
        acf_add_options_page(array(
            'page_title' => 'Maksumuurin sisällöt',
            'menu_title' => 'Maksumuurin sisällöt',
            'menu_slug' => 'paywall-settings',
            'capability' => 'manage_options',
            'icon_url' => 'dashicons-lock',
            'position' => 30
        ));

        // Add field group
        acf_add_local_field_group(array(
            'key' => 'group_paywall_settings',
            'title' => 'Maksumuurin sisällöt',
            'fields' => array(

                array(
                    'key' => 'field_paywall_title',
                    'label' => 'Maksumuuri-elementin otsikko',
                    'name' => 'paywall_title',
                    'type' => 'text',
                    'instructions' => 'Pääotsikko',
                    'default_value' => '🔒 Maksullista sisältöä',
                    'placeholder' => '🔒 Maksullista sisältöä',
                ),
                array(
                    'key' => 'field_paywall_subtitle',
                    'label' => 'Alaotsikko',
                    'name' => 'paywall_subtitle',
                    'type' => 'text',
                    'instructions' => 'Alaotsikko',
                    'default_value' => 'Sisältö on vain tilaajille.',
                    'placeholder' => 'Sisältö on vain tilaajille.',
                ),
                array(
                    'key' => 'field_paywall_content',
                    'label' => 'Maksumuurin sisältö',
                    'name' => 'paywall_content',
                    'type' => 'wysiwyg',
                    'instructions' => 'Muokkaa maksumuuri-ikkunan sisältöä. Voit käyttää kaikkia elementtejä mitä työkaluriviltä löytyy.',
                    'required' => 0,
                    // 'default_value' => $this->get_default_paywall_content(),
                    'tabs' => 'all',
                    'toolbar' => 'full',
                    'media_upload' => 1,
                    'delay' => 0,
                ),
            ),
            'location' => array(
                array(
                    array(
                        'param' => 'options_page',
                        'operator' => '==',
                        'value' => 'paywall-settings',
                    ),
                ),
            ),
            'menu_order' => 0,
            'position' => 'normal',
            'style' => 'default',
            'label_placement' => 'top',
            'instruction_placement' => 'label',
        ));
    }

    public function enqueue_admin_scripts($hook)
    {
        if ($hook !== 'edit.php') {
            return;
        }

        wp_enqueue_script('paywall-quick-edit', plugin_dir_url(__FILE__) . 'paywall-quick-edit.js', array('jquery', 'inline-edit-post'), '1.0.0', true);
        wp_localize_script('paywall-quick-edit', 'paywall_quick_edit', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('paywall_quick_edit_nonce')
        ));
    }


    public function add_meta_boxes()
    {
        // Debug: Add error log to see if this function is called


        global $post;

        add_meta_box(
            'paywall_settings_box',
            'Maksumuuri',
            array($this, 'render_meta_box'),
            'post',
            'side',
        );

        add_meta_box(
            'paywall_settings_box_page',
            'Maksumuuri',
            array($this, 'render_meta_box'),
            'ajankohtaista',
            'side',
        );
    }

    public function render_meta_box($post)
    {

        wp_nonce_field('paywall_meta_box', 'paywall_meta_box_nonce');

        $is_paywalled = get_post_meta($post->ID, '_paywall_enabled', true);

        echo '<div style="padding: 0px;">';
        echo '<label for="paywall_enabled" style="display: block; margin-bottom: 10px;">';
        echo '<input type="checkbox" id="paywall_enabled" name="paywall_enabled" value="1"' . checked($is_paywalled, 1, false) . ' />';
        echo ' <strong>Laita sisältö maksumuurin taakse</strong>';
        echo '</label>';
        echo '<p class="description" style="margin: 0; font-style: italic; color: #666;">Vain kaksi ensimmäistä kappaletta näytetään käyttäjille, joilla ei ole lukuoikeutta.</p>';
        echo '</div>';
    }

    public function save_post_meta($post_id)
    {
        if (!isset($_POST['paywall_meta_box_nonce'])) {
            return;
        }

        if (!wp_verify_nonce($_POST['paywall_meta_box_nonce'], 'paywall_meta_box')) {
            return;
        }

        if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
            return;
        }

        if (!current_user_can('edit_post', $post_id)) {
            return;
        }

        $paywall_enabled = isset($_POST['paywall_enabled']) ? 1 : 0;
        update_post_meta($post_id, '_paywall_enabled', $paywall_enabled);
    }

    public function filter_content($content)
    {
        global $post;

        if (!is_single() && !is_page()) {

            return $content;
        }



        $is_paywalled = get_post_meta($post->ID, '_paywall_enabled', true);


        if (!$is_paywalled) {
            return $content;
        }

        // Check if user has access (you can modify this logic)
        if ($this->user_has_access()) {
            return $content;
        }



        // Extract first 2 paragraphs
        $preview_content = $this->get_first_paragraphs($content, 2);

        // Add paywall
        $paywall_html = $this->get_paywall_html();

        return $preview_content . $paywall_html;
    }

    private function get_first_paragraphs($content, $paragraph_count = 2)
    {
        // Process the content directly (no need to look for .wp-block-post-content wrapper)
        return $this->process_post_content($content, $paragraph_count);
    }

    private function process_post_content($content, $paragraph_count)
    {
        // Split content by paragraph tags while preserving them
        $parts = preg_split('/(<p[^>]*>.*?<\/p>)/s', $content, -1, PREG_SPLIT_DELIM_CAPTURE | PREG_SPLIT_NO_EMPTY);

        $result = '';
        $counted_paragraphs = 0;
        $in_background_element = false;
        $background_depth = 0;

        foreach ($parts as $part) {
            // Check if this part starts a .has-background element
            if (preg_match('/<[^>]*class="[^"]*has-background[^"]*"[^>]*>/i', $part)) {
                $in_background_element = true;
                $background_depth = 1;
                $result .= $part;
                continue;
            }

            // Check if we're closing a background element
            if ($in_background_element) {
                $result .= $part;

                // Count opening and closing tags to track nesting
                $opening_tags = preg_match_all('/<[^\/][^>]*>/i', $part);
                $closing_tags = preg_match_all('/<\/[^>]*>/i', $part);
                $background_depth += $opening_tags - $closing_tags;

                if ($background_depth <= 0) {
                    $in_background_element = false;
                    $background_depth = 0;
                }
                continue;
            }

            // Check if this is a paragraph tag
            if (preg_match('/^<p[^>]*>.*?<\/p>$/s', $part)) {
                if ($counted_paragraphs < $paragraph_count) {
                    $result .= $part;
                    $counted_paragraphs++;
                } else {
                    // We've reached our limit, stop processing
                    break;
                }
            } else {
                // Not a paragraph, include it
                $result .= $part;
            }
        }

        return $result;
    }

    private function user_has_access()
    {
        //DEBUG

        // Simple check - you can expand this with your payment logic
        // For now, only logged-in users with 'subscriber' role or higher have access
        if (!is_user_logged_in()) {
            return false;
        }

        $user = wp_get_current_user();
        return in_array('subscriber', $user->roles) || current_user_can('edit_posts');
    }

    private function get_paywall_html()
    {
        ob_start();

        // Check if ACF is active and get custom content
        $custom_content = '';
        $custom_title = '';
        $custom_subtitle = '';

        if (function_exists('get_field')) {
            $custom_content = get_field('paywall_content', 'option');
            $custom_title = get_field('paywall_title', 'option');
            $custom_subtitle = get_field('paywall_subtitle', 'option');
        }

        // Use custom content if available, otherwise fallback to default
        $title = !empty($custom_title) ? $custom_title : '🔒 Maksullista sisältöä';
        $subtitle = !empty($custom_subtitle) ? $custom_subtitle : 'Tämä sisältö vaatii lukuoikeuden.';

?>
        <div class="paywall-container">
            <div class="paywall-overlay">
                <div class="paywall-content">
                    <h3><?php echo $title ?></h3>
                    <p><?php echo $subtitle ?></p>

                    <p><?php echo $custom_content ?></p>

                </div>
            </div>
        </div>


    <?php
        return ob_get_clean();
    }

    public function add_quick_edit_field($column_name, $post_type)
    {
        if ($column_name !== 'pinned_keywords' || !in_array($post_type, array('post', 'page', 'ajankohtaista'))) {
            return;
        }



    ?>
        <fieldset class="inline-edit-col-right">
            <div class="inline-edit-col">
                <label class="alignleft">
                    <input type="checkbox" name="paywall_enabled" value="1" />
                    <span class="checkbox-title">Maksumuuri päällä</span>
                </label>
            </div>
        </fieldset>

        <script type="text/javascript">
            jQuery(document).ready(function($) {
                // Add paywall status data to each row
                $('tr[id^="post-"]').each(function() {
                    var $row = $(this);
                    var post_id = $row.attr('id').replace('post-', '');
                    var has_lock = $row.find('.row-title').text().indexOf('🔒') === 0;
                    $row.attr('data-paywall-enabled', has_lock ? '1' : '0');
                });

                // Handle quick edit click
                $('.editinline').on('click', function() {
                    var post_id = $(this).closest('tr').attr('id').replace('post-', '');
                    var is_paywalled = $(this).closest('tr').attr('data-paywall-enabled') === '1';

                    // Set checkbox state
                    setTimeout(function() {
                        $('#edit-' + post_id + ' input[name="paywall_enabled"]').prop('checked', is_paywalled);
                    }, 100);
                });

                // Handle save
                $('.save').on('click', function() {
                    var $row = $(this).closest('tr');
                    var post_id = $row.attr('id').replace('edit-', '');
                    var is_checked = $row.find('input[name="paywall_enabled"]').is(':checked');

                    // Save via AJAX
                    $.post(ajaxurl, {
                        action: 'save_paywall_quick_edit',
                        post_id: post_id,
                        paywall_enabled: is_checked ? 1 : 0,
                        nonce: '<?php echo wp_create_nonce('paywall_quick_edit_nonce'); ?>'
                    });
                });
            });
        </script>
<?php
    }

    public function save_quick_edit()
    {
        if (!wp_verify_nonce($_POST['nonce'], 'paywall_quick_edit_nonce')) {
            wp_die('Security check failed');
        }

        $post_id = intval($_POST['post_id']);
        $paywall_enabled = intval($_POST['paywall_enabled']);

        if (!current_user_can('edit_post', $post_id)) {
            wp_die('Permission denied');
        }

        update_post_meta($post_id, '_paywall_enabled', $paywall_enabled);

        wp_die(); // Required for AJAX
    }
}

// Initialize the plugin
new AlfonsPaywall();
?>