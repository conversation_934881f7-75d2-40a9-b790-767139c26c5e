<?php

/**
 * Title: Login Modal
 * Slug: karprint/login-modal
 * Categories: karprint
 * Description: Modal dialog for user login
 *
 * @package WordPress
 * @subpackage Karprint

 */

?>

<!-- wp:html -->
<div id="karprint-login-modal" class="karprint-modal" style="display: none;">
    <div class="modal-overlay" id="modal-overlay"></div>
    <div class="modal-content">
        <div class="modal-header">
            <h2>Kir<PERSON><PERSON><PERSON> sisään</h2>
            <button class="modal-close" id="modal-close" aria-label="Sulje">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                </svg>
            </button>
        </div>

        <div class="modal-body">
            <div id="modal-login-messages" class="login-messages"></div>

            <form id="modal-login-form" class="karprint-login-form">
                <div class="form-group">
                    <label for="modal-username">Sähköpostiosoite</label>
                    <input
                        type="email"
                        id="modal-username"
                        name="username"
                        required
                        placeholder="<EMAIL>" />
                </div>

                <div class="form-group">
                    <label for="modal-password">Salasana</label>
                    <input
                        type="password"
                        id="modal-password"
                        name="password"
                        required
                        placeholder="Salasana" />
                </div>

                <div class="form-group checkbox-group">
                    <input
                        type="checkbox"
                        id="modal-remember"
                        name="remember" />
                    <label for="modal-remember">Muista minut</label>
                </div>

                <div class="form-group">
                    <button type="submit" id="modal-login-submit" class="login-button">
                        <span class="button-text">Kirjaudu sisään</span>
                        <span class="loading-spinner" style="display: none;">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" stroke-dasharray="31.416" stroke-dashoffset="31.416" opacity="0.3" />
                                <path d="M12 2a10 10 0 0 1 10 10" stroke="currentColor" stroke-width="4" stroke-linecap="round" />
                            </svg>
                        </span>
                    </button>
                </div>

                <div class="form-links">
                    <a href="#" id="modal-forgot-password">Unohditko salasanan?</a>
                </div>

                <input type="hidden" name="redirect_to" value="" id="modal-redirect-to" />
                <input type="hidden" name="nonce" value="" id="modal-login-nonce" />
            </form>
        </div>
    </div>
</div>

<style>
    .karprint-modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 9999;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20px;
        box-sizing: border-box;
    }

    .modal-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(2px);
    }

    .modal-content {
        position: relative;
        background: white;
        border-radius: 12px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        max-width: 450px;
        width: 100%;
        max-height: 90vh;
        overflow-y: auto;
        animation: modalSlideIn 0.3s ease-out;
    }

    @keyframes modalSlideIn {
        from {
            opacity: 0;
            transform: translateY(-20px) scale(0.95);
        }

        to {
            opacity: 1;
            transform: translateY(0) scale(1);
        }
    }

    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 24px 24px 0;
        border-bottom: 1px solid #eee;
        margin-bottom: 24px;
    }

    .modal-header h2 {
        margin: 0;
        font-size: 24px;
        color: #333;
        font-weight: 600;
    }

    .modal-close {
        background: none;
        border: none;
        cursor: pointer;
        padding: 8px;
        border-radius: 6px;
        color: #666;
        transition: all 0.2s ease;
    }

    .modal-close:hover {
        background: #f5f5f5;
        color: #333;
    }

    .modal-body {
        padding: 0 24px 24px;
    }

    .modal-body .form-group {
        margin-bottom: 20px;
    }

    .modal-body label {
        display: block;
        margin-bottom: 8px;
        font-weight: 600;
        color: #333;
        font-size: 14px;
    }

    .modal-body input[type="email"],
    .modal-body input[type="password"] {
        width: 100%;
        padding: 12px 16px;
        border: 2px solid #e1e5e9;
        border-radius: 8px;
        font-size: 16px;
        transition: all 0.2s ease;
        box-sizing: border-box;
    }

    .modal-body input[type="email"]:focus,
    .modal-body input[type="checkbox"]:focus,
    .modal-body input[type="password"]:focus {
        outline: none;
        border-color: var(--wp--preset--color--dark-accent) !important;
        box-shadow: 0 0 3px 2px var(--wp--preset--color--dark-accent) !important;
    }

    .checkbox-group {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .checkbox-group input[type="checkbox"] {
        margin: 0;
        transform: scale(1.2);
    }

    .checkbox-group label {
        margin: 0;
        font-weight: normal;
        color: #666;
        cursor: pointer;
    }

    .login-button {
        width: 100%;
        padding: 14px;
        background: var(--wp--preset--color--dark-accent) !important;
        color: white;
        border: none;
        border-radius: 8px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
    }

    .login-button:hover:not(:disabled) {
        background: #9a8139;
        transform: translateY(-1px);
    }

    .login-button:disabled {
        opacity: 0.7;
        cursor: not-allowed;
        transform: none;
    }

    .form-links {
        text-align: center;
        margin-top: 20px;
    }

    .form-links a {
        color: var(--wp--preset--color--dark-accent) !important;
        text-decoration: none;
        font-size: 14px;
        transition: color 0.2s ease;
    }

    .form-links a:hover {
        filter: brightness(1.25);
        text-decoration: underline;
    }

    .loading-spinner svg {
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% {
            transform: rotate(0deg);
        }

        100% {
            transform: rotate(360deg);
        }
    }

    .login-message {
        padding: 12px 16px;
        border-radius: 8px;
        margin-bottom: 20px;
        font-size: 14px;
        line-height: 1.4;
    }

    .login-message.error {
        background-color: #ffeaea;
        border: 1px solid #ff6b6b;
        color: #d63031;
    }

    .login-message.success {
        background-color: #eafaf1;
        border: 1px solid #00b894;
        color: #00b894;
    }

    .login-message.info {
        background-color: #e3f2fd;
        border: 1px solid #2196f3;
        color: #1976d2;
    }

    /* Mobile responsiveness */
    @media (max-width: 480px) {
        .karprint-modal {
            padding: 10px;
        }

        .modal-content {
            max-height: 95vh;
        }

        .modal-header,
        .modal-body {
            padding-left: 16px;
            padding-right: 16px;
        }
    }
</style>
<!-- /wp:html -->