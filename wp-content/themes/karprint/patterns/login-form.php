<?php

/**
 * Title: Login Form
 * Slug: karprint/login-form
 * Categories: karprint
 * Description: A complete login form with SSO authentication
 */
?>

<!-- wp:group {"style":{"spacing":{"padding":{"top":"var:preset|spacing|50","bottom":"var:preset|spacing|50"}}},"layout":{"type":"constrained","contentSize":"500px"}} -->
<div class="wp-block-group" style="padding-top:var(--wp--preset--spacing--50);padding-bottom:var(--wp--preset--spacing--50)">

    <!-- wp:heading {"textAlign":"center","level":2,"style":{"spacing":{"margin":{"bottom":"var:preset|spacing|40"}}}} -->
    <h2 class="wp-block-heading has-text-align-center" style="margin-bottom:var(--wp--preset--spacing--40)">Ki<PERSON><PERSON><PERSON><PERSON> sisään</h2>
    <!-- /wp:heading -->

    <!-- wp:html -->
    <div id="karprint-login-messages" class="login-messages" style="margin-bottom: 20px;"></div>

    <form id="karprint-login-form" class="karprint-login-form" style="background: #f9f9f9; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">

        <div class="form-group" style="margin-bottom: 20px;">
            <label for="username" style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">
                Sähköpostiosoite
            </label>
            <input
                type="email"
                id="username"
                name="username"
                required
                style="width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 4px; font-size: 16px; transition: border-color 0.3s;"
                placeholder="<EMAIL>" />
        </div>

        <div class="form-group" style="margin-bottom: 20px;">
            <label for="password" style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">
                Salasana
            </label>
            <input
                type="password"
                id="password"
                name="password"
                required
                style="width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 4px; font-size: 16px; transition: border-color 0.3s;"
                placeholder="Salasana" />
        </div>

        <div class="form-group" style="margin-bottom: 25px; display: flex; align-items: center;">
            <input
                type="checkbox"
                id="remember"
                name="remember"
                style="margin-right: 8px; transform: scale(1.2);" />
            <label for="remember" style="color: #666; cursor: pointer;">
                Muista minut
            </label>
        </div>

        <div class="form-group" style="margin-bottom: 20px;">
            <button
                type="submit"
                id="login-submit"
                style="width: 100%; padding: 14px; background: #b09642; color: white; border: none; border-radius: 4px; font-size: 16px; font-weight: 600; cursor: pointer; transition: background-color 0.3s;">
                <span class="button-text">Kirjaudu sisään</span>
                <span class="loading-spinner" style="display: none;">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="animation: spin 1s linear infinite; margin-left: 8px;">
                        <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" stroke-dasharray="31.416" stroke-dashoffset="31.416" opacity="0.3" />
                        <path d="M12 2a10 10 0 0 1 10 10" stroke="currentColor" stroke-width="4" stroke-linecap="round" />
                    </svg>
                </span>
            </button>
        </div>

        <div class="form-links" style="text-align: center; margin-top: 20px;">
            <a
                href="#"
                id="forgot-password-link"
                style="color: #b09642; text-decoration: none; font-size: 14px;">
                Unohditko salasanan?
            </a>
        </div>

        <input type="hidden" name="redirect_to" value="" id="redirect_to" />
        <input type="hidden" name="nonce" value="" id="login_nonce" />
    </form>
    <!-- /wp:html -->

</div>
<!-- /wp:group -->