<!-- wp:template-part {"slug":"header","tagName":"header"} /-->

<!-- wp:group {"tagName":"main","style":{"spacing":{"margin":{"top":"0","bottom":"0"}}},"layout":{"type":"constrained"}} -->
<main class="wp-block-group" style="margin-top: 0; margin-bottom: 0">
	<!-- wp:group {"style":{"spacing":{"padding":{"top":"var:preset|spacing|50","bottom":"var:preset|spacing|50"}}},"layout":{"type":"constrained","contentSize":"500px"}} -->
	<div
		class="wp-block-group"
		style="
			padding-top: var(--wp--preset--spacing--50);
			padding-bottom: var(--wp--preset--spacing--50);
		"
	>
		<!-- wp:heading {"textAlign":"center","level":1,"style":{"spacing":{"margin":{"bottom":"var:preset|spacing|40"}}}} -->
		<h1
			class="wp-block-heading has-text-align-center"
			style="margin-bottom: var(--wp--preset--spacing--40)"
		>
			Kirjaudu sisään
		</h1>
		<!-- /wp:heading -->

		<!-- wp:html -->
		<div
			id="karprint-login-messages"
			class="login-messages"
			style="margin-bottom: 20px"
		></div>

		<!-- wp:shortcode -->
		[logged_out_msg]
		<!-- /wp:shortcode -->

		<!-- wp:shortcode -->
		[login_error_msg]
		<!-- /wp:shortcode -->

		<form
			id="karprint-login-form"
			class="karprint-login-form"
			style="
				background: #f9f9f9;
				padding: 30px;
				border-radius: 8px;
				box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
			"
		>
			<div class="form-group" style="margin-bottom: 20px">
				<label
					for="username"
					style="
						display: block;
						margin-bottom: 8px;
						font-weight: 600;
						color: #333;
					"
				>
					Sähköpostiosoite
				</label>
				<input
					type="email"
					id="username"
					name="username"
					required
					style="
						width: 100%;
						padding: 12px;
						border: 2px solid #ddd;
						border-radius: 4px;
						font-size: 16px;
						transition: border-color 0.3s;
					"
					placeholder="<EMAIL>"
				/>
			</div>

			<div class="form-group" style="margin-bottom: 20px">
				<label
					for="password"
					style="
						display: block;
						margin-bottom: 8px;
						font-weight: 600;
						color: #333;
					"
				>
					Salasana
				</label>
				<input
					type="password"
					id="password"
					name="password"
					required
					style="
						width: 100%;
						padding: 12px;
						border: 2px solid #ddd;
						border-radius: 4px;
						font-size: 16px;
						transition: border-color 0.3s;
					"
					placeholder="Salasana"
				/>
			</div>

			<div
				class="form-group"
				style="margin-bottom: 25px; display: flex; align-items: center"
			>
				<input
					type="checkbox"
					id="remember"
					name="remember"
					style="margin-right: 8px; transform: scale(1.2)"
				/>
				<label for="remember" style="color: #666; cursor: pointer">
					Muista minut
				</label>
			</div>

			<div class="form-group" style="margin-bottom: 20px">
				<button
					type="submit"
					id="login-submit"
					style="
						width: 100%;
						padding: 14px;
						background: #b09642;
						color: white;
						border: none;
						border-radius: 4px;
						font-size: 16px;
						font-weight: 600;
						cursor: pointer;
						transition: background-color 0.3s;
					"
					onmouseover="this.style.backgroundColor='#9a8139'"
					onmouseout="this.style.backgroundColor='#b09642'"
				>
					<span class="button-text">Kirjaudu sisään</span>
					<span class="loading-spinner" style="display: none">
						<svg
							width="20"
							height="20"
							viewBox="0 0 24 24"
							fill="none"
							xmlns="http://www.w3.org/2000/svg"
							style="
								animation: spin 1s linear infinite;
								margin-left: 8px;
							"
						>
							<circle
								cx="12"
								cy="12"
								r="10"
								stroke="currentColor"
								stroke-width="4"
								stroke-dasharray="31.416"
								stroke-dashoffset="31.416"
								opacity="0.3"
							/>
							<path
								d="M12 2a10 10 0 0 1 10 10"
								stroke="currentColor"
								stroke-width="4"
								stroke-linecap="round"
							/>
						</svg>
					</span>
				</button>
			</div>

			<div
				class="form-links"
				style="text-align: center; margin-top: 20px"
			>
				<a
					href="#"
					id="forgot-password-link"
					style="
						color: #b09642;
						text-decoration: none;
						font-size: 14px;
					"
					onmouseover="this.style.textDecoration='underline'"
					onmouseout="this.style.textDecoration='none'"
				>
					Unohditko salasanan?
				</a>
			</div>

			<input type="hidden" name="redirect_to" value="" id="redirect_to" />
			<input type="hidden" name="nonce" value="" id="login_nonce" />
		</form>

		<style>
			@keyframes spin {
			    0% { transform: rotate(0deg); }
			    100% { transform: rotate(360deg); }
			}

			.karprint-login-form input:focus {
			    outline: none;
			    border-color: var(--wp--preset--color--dark-accent) !important;
			    box-shadow: 0 0 0 3px var(--wp--preset--color--dark-accent);
			}

			.login-message {
			    padding: 12px 16px;
			    border-radius: 4px;
			    margin-bottom: 15px;
			    font-size: 14px;
			}

			.login-message.error {
			    background-color: #ffeaea;
			    border: 1px solid #ff6b6b;
			    color: #d63031;
			}

			.login-message.success {
			    background-color: #eafaf1;
			    border: 1px solid: #00b894;
			    color: #00b894;
			}

			.login-message.info {
			    background-color: #e3f2fd;
			    border: 1px solid #2196f3;
			    color: #1976d2;
			}

			#karprint-login-form.loading {
			    opacity: 0.7;
			    pointer-events: none;
			}
		</style>

		<script>
			document.addEventListener("DOMContentLoaded", function () {
				const form = document.getElementById("karprint-login-form");
				const messagesContainer = document.getElementById(
					"karprint-login-messages",
				);
				const submitButton = document.getElementById("login-submit");
				const buttonText = submitButton.querySelector(".button-text");
				const loadingSpinner =
					submitButton.querySelector(".loading-spinner");
				const forgotPasswordLink = document.getElementById(
					"forgot-password-link",
				);

				// Set nonce and redirect URL
				const urlParams = new URLSearchParams(window.location.search);
				document.getElementById("redirect_to").value =
					urlParams.get("redirect_to") || "/";
				document.getElementById("login_nonce").value =
					karprint_ajax.nonce;

				// Show any URL-based error messages
				if (urlParams.get("login") === "failed") {
					showMessage("Väärä käyttäjätunnus tai salasana", "error");
				}

				function showMessage(message, type = "info") {
					messagesContainer.innerHTML = `<div class="login-message ${type}">${message}</div>`;
					messagesContainer.scrollIntoView({
						behavior: "smooth",
						block: "nearest",
					});
				}

				function setLoading(loading) {
					if (loading) {
						form.classList.add("loading");
						buttonText.style.display = "none";
						loadingSpinner.style.display = "inline-block";
						submitButton.disabled = true;
					} else {
						form.classList.remove("loading");
						buttonText.style.display = "inline";
						loadingSpinner.style.display = "none";
						submitButton.disabled = false;
					}
				}

				form.addEventListener("submit", function (e) {
					e.preventDefault();

					const formData = new FormData(form);
					formData.append("action", "karprint_login");

					setLoading(true);
					messagesContainer.innerHTML = "";

					fetch(karprint_ajax.ajax_url, {
						method: "POST",
						body: formData,
					})
						.then((response) => response.json())
						.then((data) => {
							setLoading(false);

							if (data.success) {
								showMessage(data.data.message, "success");
								setTimeout(() => {
									window.location.href =
										data.data.redirect_to;
								}, 1000);
							} else {
								showMessage(data.data.message, "error");
							}
						})
						.catch((error) => {
							setLoading(false);
							showMessage(
								"Kirjautumisessa tapahtui virhe. Yritä myöhemmin uudelleen.",
								"error",
							);
							console.error("Login error:", error);
						});
				});

				forgotPasswordLink.addEventListener("click", function (e) {
					e.preventDefault();

					const email = document.getElementById("username").value;
					if (!email) {
						showMessage(
							"Syötä ensin sähköpostiosoitteesi.",
							"error",
						);
						document.getElementById("username").focus();
						return;
					}

					const formData = new FormData();
					formData.append("action", "karprint_password_reset");
					formData.append("email", email);
					formData.append("nonce", karprint_ajax.nonce);

					setLoading(true);

					fetch(karprint_ajax.ajax_url, {
						method: "POST",
						body: formData,
					})
						.then((response) => response.json())
						.then((data) => {
							setLoading(false);

							if (data.success) {
								showMessage(data.data.message, "success");
								if (data.data.redirect_to) {
									setTimeout(() => {
										window.open(
											data.data.redirect_to,
											"_blank",
										);
									}, 2000);
								}
							} else {
								showMessage(data.data.message, "error");
							}
						})
						.catch((error) => {
							setLoading(false);
							showMessage(
								"Salasanan palautuksessa tapahtui virhe.",
								"error",
							);
							console.error("Password reset error:", error);
						});
				});
			});
		</script>
		<!-- /wp:html -->
	</div>
	<!-- /wp:group -->
</main>
<!-- /wp:group -->

<!-- wp:template-part {"slug":"footer","tagName":"footer"} /-->
<!-- wp:template-part {"slug":"login-modal","theme":"karprint"} /-->
