/**
 * Ka<PERSON><PERSON>t Login Functionality
 * Handles SSO authentication and form interactions
 */

document.addEventListener("DOMContentLoaded", function () {
	const form = document.getElementById("karprint-login-form");

	// Only initialize if login form exists
	if (!form) return;

	const messagesContainer = document.getElementById(
		"karprint-login-messages",
	);
	const submitButton = document.getElementById("login-submit");
	const buttonText = submitButton?.querySelector(".button-text");
	const loadingSpinner = submitButton?.querySelector(".loading-spinner");
	const forgotPasswordLink = document.getElementById("forgot-password-link");

	// Initialize form
	initializeForm();

	function initializeForm() {
		// Set nonce and redirect URL
		const urlParams = new URLSearchParams(window.location.search);
		const redirectInput = document.getElementById("redirect_to");
		const nonceInput = document.getElementById("login_nonce");

		if (redirectInput) {
			redirectInput.value =
				urlParams.get("redirect_to") || "/jasensivut/";
		}

		if (nonceInput && typeof karprint_ajax !== "undefined") {
			nonceInput.value = karprint_ajax.nonce;
		}

		// Show any URL-based error messages
		if (urlParams.get("login") === "failed") {
			showMessage("Väärä käyttäjätunnus tai salasana", "error");
		}

		// Add event listeners
		form.addEventListener("submit", handleLogin);

		if (forgotPasswordLink) {
			forgotPasswordLink.addEventListener("click", handlePasswordReset);
		}

		// Add input focus effects
		addInputEffects();
	}

	function addInputEffects() {
		const inputs = form.querySelectorAll(
			'input[type="email"], input[type="password"]',
		);

		inputs.forEach((input) => {
			input.addEventListener("focus", function () {
				this.style.borderColor = "#b09642";
				this.style.boxShadow = "0 0 0 3px rgba(176, 150, 66, 0.1)";
			});

			input.addEventListener("blur", function () {
				this.style.borderColor = "#ddd";
				this.style.boxShadow = "none";
			});
		});

		// Add hover effect to submit button
		if (submitButton) {
			submitButton.addEventListener("mouseenter", function () {
				if (!this.disabled) {
					this.style.backgroundColor = "#9a8139";
				}
			});

			submitButton.addEventListener("mouseleave", function () {
				if (!this.disabled) {
					this.style.backgroundColor = "#b09642";
				}
			});
		}

		// Add hover effect to forgot password link
		if (forgotPasswordLink) {
			forgotPasswordLink.addEventListener("mouseenter", function () {
				this.style.textDecoration = "underline";
			});

			forgotPasswordLink.addEventListener("mouseleave", function () {
				this.style.textDecoration = "none";
			});
		}
	}

	function showMessage(message, type = "info") {
		if (!messagesContainer) return;

		const messageHtml = `<div class="login-message ${type}">${message}</div>`;
		messagesContainer.innerHTML = messageHtml;
		messagesContainer.scrollIntoView({
			behavior: "smooth",
			block: "nearest",
		});

		// Auto-hide success messages after 5 seconds
		if (type === "success") {
			setTimeout(() => {
				messagesContainer.innerHTML = "";
			}, 5000);
		}
	}

	function setLoading(loading) {
		if (!submitButton || !buttonText || !loadingSpinner) return;

		if (loading) {
			form.classList.add("loading");
			buttonText.style.display = "none";
			loadingSpinner.style.display = "inline-block";
			submitButton.disabled = true;
			submitButton.style.backgroundColor = "#999";
		} else {
			form.classList.remove("loading");
			buttonText.style.display = "inline";
			loadingSpinner.style.display = "none";
			submitButton.disabled = false;
			submitButton.style.backgroundColor = "#b09642";
		}
	}

	function handleLogin(e) {
		e.preventDefault();

		// Check if AJAX is available
		if (typeof karprint_ajax === "undefined") {
			showMessage(
				"Kirjautumispalvelu ei ole käytettävissä. Päivitä sivu ja yritä uudelleen.",
				"error",
			);
			return;
		}

		const formData = new FormData(form);
		formData.append("action", "karprint_login");

		// Validate form
		const username = formData.get("username");
		const password = formData.get("password");

		if (!username || !password) {
			showMessage("Käyttäjätunnus ja salasana ovat pakollisia.", "error");
			return;
		}

		if (!isValidEmail(username)) {
			showMessage("Syötä kelvollinen sähköpostiosoite.", "error");
			return;
		}

		setLoading(true);
		messagesContainer.innerHTML = "";

		fetch(karprint_ajax.ajax_url, {
			method: "POST",
			body: formData,
		})
			.then((response) => {
				if (!response.ok) {
					throw new Error("Network response was not ok");
				}
				return response.json();
			})
			.then((data) => {
				setLoading(false);

				if (data.success) {
					showMessage(data.data.message, "success");

					// Redirect after short delay
					setTimeout(() => {
						window.location.href = data.data.redirect_to;
					}, 1000);
				} else {
					showMessage(
						data.data.message || "Kirjautuminen epäonnistui.",
						"error",
					);
				}
			})
			.catch((error) => {
				setLoading(false);
				showMessage(
					"Kirjautumisessa tapahtui virhe. Tarkista internetyhteytesi ja yritä uudelleen.",
					"error",
				);
				console.error("Login error:", error);
			});
	}

	function handlePasswordReset(e) {
		e.preventDefault();

		const emailInput = document.getElementById("username");
		const email = emailInput?.value;

		if (!email) {
			showMessage("Syötä ensin sähköpostiosoitteesi.", "error");
			emailInput?.focus();
			return;
		}

		if (!isValidEmail(email)) {
			showMessage("Syötä kelvollinen sähköpostiosoite.", "error");
			emailInput?.focus();
			return;
		}

		// Check if AJAX is available
		if (typeof karprint_ajax === "undefined") {
			// Fallback: redirect directly to SSO password reset
			window.open(
				`https://sso.atexsoftware.fi/karprint/password-reset?email=${encodeURIComponent(email)}`,
				"_blank",
			);
			return;
		}

		const formData = new FormData();
		formData.append("action", "karprint_password_reset");
		formData.append("email", email);
		formData.append("nonce", karprint_ajax.nonce);

		setLoading(true);

		fetch(karprint_ajax.ajax_url, {
			method: "POST",
			body: formData,
		})
			.then((response) => response.json())
			.then((data) => {
				setLoading(false);

				if (data.success) {
					showMessage(data.data.message, "success");

					if (data.data.redirect_to) {
						setTimeout(() => {
							window.open(data.data.redirect_to, "_blank");
						}, 2000);
					}
				} else {
					showMessage(
						data.data.message || "Salasanan palautus epäonnistui.",
						"error",
					);
				}
			})
			.catch((error) => {
				setLoading(false);
				showMessage(
					"Salasanan palautuksessa tapahtui virhe. Yritä myöhemmin uudelleen.",
					"error",
				);
				console.error("Password reset error:", error);
			});
	}

	function isValidEmail(email) {
		const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
		return emailRegex.test(email);
	}
});

// Add CSS styles dynamically
const loginStyles = `
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    .karprint-login-form input:focus {
        outline: none !important;
        border-color: var(--wp--preset--color--dark-accent) !important;
        box-shadow: 0 0 0 3px var(--wp--preset--color--dark-accent) !important;
    }
    
    .login-message {
        padding: 12px 16px;
        border-radius: 4px;
        margin-bottom: 15px;
        font-size: 14px;
        line-height: 1.4;
    }
    
    .login-message.error {
        background-color: #ffeaea;
        border: 1px solid #ff6b6b;
        color: #d63031;
    }
    
    .login-message.success {
        background-color: #eafaf1;
        border: 1px solid #00b894;
        color: #00b894;
    }
    
    .login-message.info {
        background-color: #e3f2fd;
        border: 1px solid #2196f3;
        color: #1976d2;
    }
    
    #karprint-login-form.loading {
        opacity: 0.7;
        pointer-events: none;
    }
    
    .karprint-login-form button:disabled {
        cursor: not-allowed !important;
        opacity: 0.7;
    }
`;

// Inject styles
const styleSheet = document.createElement("style");
styleSheet.textContent = loginStyles;
document.head.appendChild(styleSheet);
