/**
 * Login Modal Functionality
 * Handles modal opening, closing, and form submission
 */

document.addEventListener("DOMContentLoaded", function () {
	const modal = document.getElementById("karprint-login-modal");
	const openButton = document.getElementById("header-login-btn");
	const closeButton = document.getElementById("modal-close");
	const overlay = document.getElementById("modal-overlay");
	const form = document.getElementById("modal-login-form");
	const messagesContainer = document.getElementById("modal-login-messages");

	// Only initialize if modal exists
	if (!modal) return;

	const submitButton = document.getElementById("modal-login-submit");
	const buttonText = submitButton?.querySelector(".button-text");
	const loadingSpinner = submitButton?.querySelector(".loading-spinner");
	const forgotPasswordLink = document.getElementById("modal-forgot-password");

	// Initialize modal functionality
	initializeModal();

	function initializeModal() {
		console.log("Karprint Login Modal: Initializing...");
		// Set up event listeners for header login button
		if (openButton) {
			openButton.addEventListener("click", openModal);
		}

		// Set up event listeners for all .log-in links
		const loginLinks = document.querySelectorAll(".log-in, .nav-login a");
		console.log(`Found ${loginLinks.length} login links:`, loginLinks);

		loginLinks.forEach((link) => {
			link.addEventListener("click", function (e) {
				console.log(
					"Login link clicked:",
					this,
					"href:",
					this.getAttribute("href"),
				);
				// Only prevent default if this is a login link that should open modal
				const href = this.getAttribute("href");

				e.preventDefault();
				console.log("Opening modal from login link");
				openModal();
			});
		});

		// Use event delegation for dynamically added login links
		// document.addEventListener("click", function (e) {
		// 	const target = e.target.closest(
		// 		'.log-in, .nav-login a, a[href*="login"], a[class*="login"]',
		// 	);
		// 	if (target) {
		// 		console.log("Event delegation caught login link:", target);
		// 		const href = target.getAttribute("href");
		// 		if (
		// 			href &&
		// 			(href.includes("login") ||
		// 				href === "#" ||
		// 				href === "#login" ||
		// 				target.classList.contains("log-in"))
		// 		) {
		// 			e.preventDefault();
		// 			console.log("Opening modal via event delegation");
		// 			openModal();
		// 		}
		// 	}
		// });

		if (closeButton) {
			closeButton.addEventListener("click", closeModal);
		}

		if (overlay) {
			overlay.addEventListener("click", closeModal);
		}

		// Close modal on Escape key
		document.addEventListener("keydown", function (e) {
			if (e.key === "Escape" && modal.style.display !== "none") {
				closeModal();
			}
		});

		// Handle form submission
		if (form) {
			form.addEventListener("submit", handleModalLogin);
		}

		// Handle forgot password
		if (forgotPasswordLink) {
			forgotPasswordLink.addEventListener(
				"click",
				handleModalPasswordReset,
			);
		}

		// Set up form data
		setupFormData();

		// Add input effects
		addModalInputEffects();
	}

	function openModal() {
		modal.style.display = "flex";
		document.body.style.overflow = "hidden";

		// Focus on first input
		const firstInput = modal.querySelector('input[type="email"]');
		if (firstInput) {
			setTimeout(() => firstInput.focus(), 100);
		}

		// Clear any previous messages
		if (messagesContainer) {
			messagesContainer.innerHTML = "";
		}
	}

	function closeModal() {
		modal.style.display = "none";
		document.body.style.overflow = "";

		// Reset form
		if (form) {
			form.reset();
		}

		// Clear messages
		if (messagesContainer) {
			messagesContainer.innerHTML = "";
		}

		// Reset loading state
		setModalLoading(false);
	}

	function setupFormData() {
		// Set nonce and redirect URL
		const redirectInput = document.getElementById("modal-redirect-to");
		const nonceInput = document.getElementById("modal-login-nonce");

		if (redirectInput) {
			redirectInput.value =
				window.location.pathname === "/login/"
					? "/jasensivut/"
					: window.location.href;
		}

		if (nonceInput && typeof karprint_ajax !== "undefined") {
			nonceInput.value = karprint_ajax.nonce;
		}
	}

	function addModalInputEffects() {
		const inputs = modal.querySelectorAll(
			'input[type="email"], input[type="password"]',
		);

		inputs.forEach((input) => {
			input.addEventListener("focus", function () {
				this.style.borderColor = "#b09642";
				this.style.boxShadow = "0 0 0 3px rgba(176, 150, 66, 0.1)";
			});

			input.addEventListener("blur", function () {
				this.style.borderColor = "#e1e5e9";
				this.style.boxShadow = "none";
			});
		});
	}

	function showModalMessage(message, type = "info") {
		if (!messagesContainer) return;

		const messageHtml = `<div class="login-message ${type}">${message}</div>`;
		messagesContainer.innerHTML = messageHtml;

		// Auto-hide success messages after 3 seconds
		if (type === "success") {
			setTimeout(() => {
				messagesContainer.innerHTML = "";
			}, 3000);
		}
	}

	function setModalLoading(loading) {
		if (!submitButton || !buttonText || !loadingSpinner) return;

		if (loading) {
			form.classList.add("loading");
			buttonText.style.display = "none";
			loadingSpinner.style.display = "inline-block";
			submitButton.disabled = true;
		} else {
			form.classList.remove("loading");
			buttonText.style.display = "inline";
			loadingSpinner.style.display = "none";
			submitButton.disabled = false;
		}
	}

	function handleModalLogin(e) {
		e.preventDefault();

		// Check if AJAX is available
		if (typeof karprint_ajax === "undefined") {
			showModalMessage(
				"Kirjautumispalvelu ei ole käytettävissä. Päivitä sivu ja yritä uudelleen.",
				"error",
			);
			return;
		}

		const formData = new FormData(form);
		formData.append("action", "karprint_login");

		// Validate form
		const username = formData.get("username");
		const password = formData.get("password");

		if (!username || !password) {
			showModalMessage(
				"Käyttäjätunnus ja salasana ovat pakollisia.",
				"error",
			);
			return;
		}

		if (!isValidEmail(username)) {
			showModalMessage("Syötä kelvollinen sähköpostiosoite.", "error");
			return;
		}

		setModalLoading(true);
		messagesContainer.innerHTML = "";

		fetch(karprint_ajax.ajax_url, {
			method: "POST",
			body: formData,
		})
			.then((response) => {
				if (!response.ok) {
					throw new Error("Network response was not ok");
				}
				return response.json();
			})
			.then((data) => {
				setModalLoading(false);

				if (data.success) {
					showModalMessage(data.data.message, "success");

					// Close modal and redirect after short delay
					setTimeout(() => {
						closeModal();
						window.location.href = data.data.redirect_to;
					}, 1000);
				} else {
					showModalMessage(
						data.data.message || "Kirjautuminen epäonnistui.",
						"error",
					);
				}
			})
			.catch((error) => {
				setModalLoading(false);
				showModalMessage(
					"Kirjautumisessa tapahtui virhe. Tarkista internetyhteytesi ja yritä uudelleen.",
					"error",
				);
				console.error("Modal login error:", error);
			});
	}

	function handleModalPasswordReset(e) {
		e.preventDefault();

		const emailInput = document.getElementById("modal-username");
		const email = emailInput?.value;

		if (!email) {
			showModalMessage("Syötä ensin sähköpostiosoitteesi.", "error");
			emailInput?.focus();
			return;
		}

		if (!isValidEmail(email)) {
			showModalMessage("Syötä kelvollinen sähköpostiosoite.", "error");
			emailInput?.focus();
			return;
		}

		// Check if AJAX is available
		if (typeof karprint_ajax === "undefined") {
			// Fallback: redirect directly to SSO password reset
			window.open(
				`https://sso.atexsoftware.fi/karprint/password-reset?email=${encodeURIComponent(email)}`,
				"_blank",
			);
			return;
		}

		const formData = new FormData();
		formData.append("action", "karprint_password_reset");
		formData.append("email", email);
		formData.append("nonce", karprint_ajax.nonce);

		setModalLoading(true);

		fetch(karprint_ajax.ajax_url, {
			method: "POST",
			body: formData,
		})
			.then((response) => response.json())
			.then((data) => {
				setModalLoading(false);

				if (data.success) {
					showModalMessage(data.data.message, "success");

					if (data.data.redirect_to) {
						setTimeout(() => {
							window.open(data.data.redirect_to, "_blank");
						}, 2000);
					}
				} else {
					showModalMessage(
						data.data.message || "Salasanan palautus epäonnistui.",
						"error",
					);
				}
			})
			.catch((error) => {
				setModalLoading(false);
				showModalMessage(
					"Salasanan palautuksessa tapahtui virhe. Yritä myöhemmin uudelleen.",
					"error",
				);
				console.error("Modal password reset error:", error);
			});
	}

	function isValidEmail(email) {
		const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
		return emailRegex.test(email);
	}

	// Expose functions globally for potential external use
	window.KarprintModal = {
		open: openModal,
		close: closeModal,
	};
});
