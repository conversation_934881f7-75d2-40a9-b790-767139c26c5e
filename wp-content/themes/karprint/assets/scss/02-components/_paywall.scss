.paywall-container {
	position: relative;
	margin: 20px 0;
}

.paywall-overlay {
	background: linear-gradient(
		180deg,
		rgba(255, 255, 255, 0) 0%,
		rgba(255, 255, 255, 0.95) 15%,
		rgba(255, 255, 255, 1) 100%
	);
	/* border: 2px solid #e1e5e9; */
	border-radius: 8px;
	padding: 30px 20px;
	text-align: center;
	margin-top: -100px;
	position: relative;
	z-index: 10;
}

.paywall-content h3 {
	color: #2c3e50;
	margin-top: 80px;
	margin-bottom: 10px;
	font-size: 24px;
}

.paywall-content p {
	color: #5a6c7d;
	margin-bottom: 20px;
}

.paywall-actions {
	margin: 20px 0;
}

.paywall-btn {
	display: inline-block;
	padding: 12px 24px;
	margin: 0 10px;
	text-decoration: none;
	border-radius: 5px;
	font-weight: bold;
	transition: all 0.3s ease;
}

.paywall-btn-login {
	background-color: #3498db;
	color: white;
}

.paywall-btn-register,
.paywall-btn-subscribe {
	background-color: #e74c3c;
	color: white;
}

.paywall-btn:hover {
	transform: translateY(-2px);
	box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.paywall-benefits {
	font-size: 14px;
	color: #7f8c8d;
	border-top: 1px solid #ecf0f1;
	padding-top: 15px;
	margin-top: 20px;
}

@media (max-width: 768px) {
	.paywall-btn {
		display: block;
		margin: 10px 0;
	}
}
