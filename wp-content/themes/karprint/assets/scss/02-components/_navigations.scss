.main-menu .wp-block-navigation-link a:hover:not(.mobile-extra-links a),
.main-menu .wp-block-navigation-link a:active:not(.mobile-extra-links a),
.main-menu .current-menu-item:not(.mobile-extra-links .current-menu-item) {
	text-decoration: underline 2px var(--wp--preset--color--yellow) !important;
	text-underline-offset: 6px;
}

.top-nav-links .current-menu-item {
	text-decoration: underline 1px var(--wp--preset--color--white) !important;
}

.nav-logo .custom-logo {
	max-width: 160px !important;
	min-width: 160px !important;
}

.nav-login,
.log-in {
	position: relative;
	padding-right: 2.5rem !important;
}

.nav-login a::after,
.log-in::after {
	content: "";
	display: inline-block;
	position: absolute;
	top: 0;
	right: 0;
	width: 23px;
	height: 23px;
	background-image: url("../../img/misc/circle-user-kirjaudu.svg");
	background-size: contain;
	background-repeat: no-repeat;
}

/* Make .log-in links clickable and styled like login links */
.log-in {
	cursor: pointer;
	text-decoration: none;
	transition: all 0.2s ease;
}

.log-in:hover {
	text-decoration: underline;
}

.nav-karprint {
	position: relative;
	padding-left: 3rem !important;
}

.nav-karprint::after {
	content: "";
	display: inline-block;
	position: absolute;
	top: -7px;
	left: 0;
	width: 33px;
	height: 33px;
	background-image: url("../../img/logo/karprint-logo.png");
	background-size: contain;
	background-repeat: no-repeat;
}

.top-nav-links li::after {
	content: "";
	position: absolute;
	right: calc(-1 * var(--wp--preset--spacing--big) / 2);
	width: 1px;
	height: 25px;
	background-color: currentColor;
}

.top-nav-links li:last-child::after {
	display: none;
}

@media screen and (max-width: 1000px) {
	.main-menu {
		width: 340px;
	}
}

@media screen and (max-width: 800px) {
	.nav-logo .custom-logo {
		max-width: 120px !important;
		min-width: 120px !important;
	}

	.main-menu * {
		font-size: var(--wp--preset--font-size--small);
	}
}

.wp-block-karprint-subpage-navigation .nav-title,
.wp-block-karprint-subpage-navigation .subpage-navigation {
	padding-left: 0 !important;

	@media screen and (max-width: 768px) {
		display: none;
	}
}

#site-navigation-mobile {
	.current_page_ancestor a.current-menu-ancestor,
	.current-menu-item {
		// background-color: #ffd5c5;
		display: block;
	}

	a.current-menu-item {
		// padding-left: 1rem;
		text-decoration: underline;
	}

	.current-menu-item ul li a {
		padding-left: 1rem;
	}
}

#menu-toggle-side {
	position: relative;
	transform: translate(30px, -50px);
	font-size: 1.4rem;
}

#hamburger span {
	height: 0.2rem !important;
}

@media screen and (max-width: 768px) {
	.grid-cols-header-home {
		grid-template-columns: 0 1fr 18%;
		.header-center {
			justify-content: start;
			a {
				max-width: 165px;
				position: relative;
				left: -1rem;
			}
		}
	}
}

@media (min-width: 700px) {
	.wp-block-navigation-item.frontpage .wp-block-navigation-item__label {
		position: relative;
		display: inline-block;
		width: 19px;
		height: 17px;
		overflow: hidden;
		text-indent: 100%;
		white-space: nowrap;
		background-image: url("../../img/misc/frontpage-icon.svg");
		background-repeat: no-repeat;
		background-size: contain;
		background-position: center;
	}
}

@media (max-width: 699px) {
	.main-menu {
		width: auto;
	}

	ul.main-menu {
		gap: var(--wp--preset--spacing--xs) !important;
	}

	.main-menu .wp-block-navigation__container {
		display: none;
	}

	.wp-block-navigation__responsive-container-content {
		padding-top: 0 !important;
	}

	.main-menu .wp-block-navigation__responsive-container-open {
		display: block;
	}

	.main-menu .wp-block-navigation__toggle {
		display: inline-block;
	}

	.main-menu
		.wp-block-navigation__responsive-container-open:not(.always-shown) {
		display: block;
	}

	.top-nav-links .wp-block-navigation-item:nth-child(1),
	.top-nav-links .wp-block-navigation-item:nth-child(2),
	.navigation-search {
		display: none !important;
	}

	.main-menu .wp-block-navigation__responsive-container-open svg {
		height: 40px !important;
		width: 40px !important;
	}

	.main-menu .wp-block-navigation__responsive-container-close svg {
		height: 40px !important;
		width: 40px !important;
	}

	.main-menu .wp-block-navigation__responsive-container-close {
		margin-top: -65px;
		margin-right: 47px !important;
		background-color: white !important;
	}

	.nav-logo .custom-logo {
		max-width: 80px !important;
		min-width: 80px !important;
	}

	.main-menu-group {
		gap: 0 !important;
	}

	.main-menu-wrapper {
		padding-left: var(--wp--preset--spacing--md) !important;
		padding-right: var(--wp--preset--spacing--md) !important;
	}

	.main-menu-wrapper *:where(.is-layout-flex) {
		gap: 0;
	}

	.main-menu-group .menu-subscribe {
		order: 1;
	}

	.main-menu-group .wp-block-group {
		order: 2;
	}

	.is-menu-open {
		margin-top: 143px !important;
		overflow: visible !important;
		padding: 0 !important;
		//height: calc(100vh - 170px) !important;
		-webkit-overflow-scrolling: touch;
	}

	.wp-block-navigation.items-justified-center {
		--navigation-layout-justification-setting: left;
	}

	.main-menu ul {
		width: 100%;
		padding-top: var(--wp--preset--spacing--md) !important;
		padding-left: var(--wp--preset--spacing--md) !important;
		padding-right: var(--wp--preset--spacing--md) !important;
	}

	.main-menu li * {
		//font-size: var(--wp--preset--font-size--xxlarge) !important;
	}

	.main-menu li {
		width: 100%;
		border-bottom: 2px solid #dedede !important;
	}

	.main-menu li a {
		margin-bottom: var(--wp--preset--spacing--xs);
		padding-left: var(--wp--preset--spacing--xs) !important;
		padding-right: var(--wp--preset--spacing--xs) !important;
	}

	.main-menu li a::after {
		content: "";
		display: inline-block;
		position: absolute;
		top: 10px;
		right: 0;
		width: 7px;
		height: 10px;
		background-image: url("../../img/misc/arrow.svg");
		background-size: contain;
		background-repeat: no-repeat;
		padding-left: var(--wp--preset--spacing--xs) !important;
		padding-right: var(--wp--preset--spacing--xs) !important;
	}

	.wp-block-navigation-item__content {
		text-decoration: none;
	}

	.is-menu-open .navigation-search {
		display: block !important;
		max-width: 100%;
		width: auto;
		margin-top: var(--wp--preset--spacing--sm) !important;
		margin-left: var(--wp--preset--spacing--md) !important;
		margin-right: var(--wp--preset--spacing--md) !important;
	}

	.mobile-extra-links {
		display: none;
	}

	.is-menu-open .mobile-extra-links {
		display: block;
		margin-top: var(--wp--preset--spacing--sm) !important;
		background-color: var(--wp--preset--color--green);
		color: white;
		border-bottom: none !important;
		padding-left: var(--wp--preset--spacing--md) !important;
		padding-right: var(--wp--preset--spacing--md) !important;
	}

	.mobile-extra-links li {
		border-bottom: none !important;
	}

	.mobile-extra-links li:nth-child(1) {
		padding-top: var(--wp--preset--spacing--xs) !important;
	}

	.main-menu .nav-login a::after,
	.main-menu .log-in::after,
	.mobile-extra-links li a::after {
		display: none;
	}

	.mobile-extra-links li a:hover,
	.mobile-extra-links li a:active,
	.mobile-extra-links li.current-menu-item a,
	.top-nav-links .nav-login a:hover,
	.top-nav-links .nav-login a:active,
	.top-nav-links .nav-login.current-menu-item a,
	.top-nav-links .log-in:hover,
	.top-nav-links .log-in:active {
		text-decoration: underline 1px white !important;
	}

	.mobile-login-clone span:hover,
	.mobile-login-clone span:active,
	.mobile-login-clone .current-menu-item {
		text-decoration: underline 2px var(--wp--preset--color--yellow) !important;
		text-underline-offset: 6px;
	}

	.mobile-login-clone span {
		display: inline-flex;
		align-items: center;
		gap: var(--wp--preset--spacing--xs) !important;
	}
	.mobile-login-clone span::after {
		content: "";
		width: 23px;
		height: 23px;
		background-image: url("../../img/misc/circle-user-kirjaudu-mobile.svg");
		background-size: contain;
		background-repeat: no-repeat;
	}
}
