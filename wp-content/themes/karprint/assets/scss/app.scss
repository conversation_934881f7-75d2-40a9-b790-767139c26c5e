@import "variables";
@import "typography";

/* Tailwind CSS, change settings from tailwind.config.js */
@import "base";
@tailwind components;
@import "utilities";

/* Single post css */
@import "single";

/* Generic CSS for front end */
@import "01-generic/accessibility";
@import "01-generic/layout";
@import "01-generic/forms";
@import "01-generic/fade-up-animation";

/* Components CSS for front end */
@import "02-components/comments";
@import "02-components/cookiebot";
@import "02-components/swal";
@import "02-components/menu";
@import "02-components/breadcrumbs";
@import "02-components/tribe-overrides";
@import "02-components/login";
@import "02-components/posts-wrapper";
@import "02-components/navigations";
@import "02-components/newsletter";
@import "02-components/search";
@import "02-components/article-card";
@import "02-components/footer";
@import "02-components/paywall";

/* Patterns */
// @import '_blocks/pattern-banner';
// @import '_blocks/pattern-theme-posts';
// @import '_blocks/pattern-mediatext-lift';

@import "_blocks/core-list";
@import "_blocks/custom-promo";
@import "_blocks/core-link";
@import "_blocks/core-summary";
